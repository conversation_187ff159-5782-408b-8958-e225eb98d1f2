{"name": "react-tictactoe", "version": "0.0.0", "main": "/src/index.js", "homepage": "https://dariogeorge21.github.io/tic-tac-toe-react", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build -repo=https://github.com/dariogeorge21/tic-tac-toe-react.git"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "^5.0.0"}, "devDependencies": {"gh-pages": "^6.3.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}